// YouTube Watch Progress Saver - Content Script
(function() {
    'use strict';
    
    let currentVideoId = null;
    let videoElement = null;
    let saveInterval = null;
    let isInitialized = false;
    
    // Configuration
    const SAVE_INTERVAL_MS = 5000; // Save every 5 seconds
    const MIN_WATCH_TIME = 10; // Minimum seconds before saving progress
    const STORAGE_PREFIX = 'yt_progress_';
    
    // Extract video ID from YouTube URL
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }

    // Extract timestamp from YouTube URL (t parameter)
    function getUrlTimestamp() {
        const urlParams = new URLSearchParams(window.location.search);
        const tParam = urlParams.get('t');
        if (tParam) {
            // Handle formats like "89s", "1m30s", "90"
            if (tParam.includes('m') || tParam.includes('s')) {
                let seconds = 0;
                const minutesMatch = tParam.match(/(\d+)m/);
                const secondsMatch = tParam.match(/(\d+)s/);
                if (minutesMatch) seconds += parseInt(minutesMatch[1]) * 60;
                if (secondsMatch) seconds += parseInt(secondsMatch[1]);
                return seconds;
            } else {
                // Just a number in seconds
                return parseInt(tParam);
            }
        }
        return null;
    }
    
    // Get the video element
    function getVideoElement() {
        return document.querySelector('video');
    }
    
    // Save current progress to storage
    async function saveProgress() {
        if (!videoElement || !currentVideoId) {
            console.log('Cannot save progress: missing video element or video ID');
            return;
        }

        const currentTime = videoElement.currentTime;
        const duration = videoElement.duration;

        console.log(`Attempting to save progress: ${Math.floor(currentTime)}s / ${Math.floor(duration)}s`);

        // Don't save if video just started or is almost finished
        if (currentTime < MIN_WATCH_TIME) {
            console.log(`Not saving: video time ${currentTime}s is less than minimum ${MIN_WATCH_TIME}s`);
            return;
        }

        if (currentTime > duration - 30) {
            console.log(`Not saving: video is almost finished (${Math.floor(duration - currentTime)}s remaining)`);
            return;
        }

        const progressData = {
            videoId: currentVideoId,
            timestamp: currentTime,
            duration: duration,
            lastSaved: Date.now(),
            url: window.location.href
        };

        try {
            await chrome.storage.local.set({
                [STORAGE_PREFIX + currentVideoId]: progressData
            });
            console.log(`✅ Progress saved for ${currentVideoId}: ${Math.floor(currentTime)}s / ${Math.floor(duration)}s`);

            // Also save for crash recovery
            saveCrashRecoveryData();
        } catch (error) {
            console.error('❌ Failed to save progress:', error);
        }
    }
    
    // Load and restore progress
    async function loadProgress() {
        if (!currentVideoId) return;

        try {
            // Check for URL timestamp first (takes priority)
            const urlTimestamp = getUrlTimestamp();

            // Get saved progress
            const result = await chrome.storage.local.get(STORAGE_PREFIX + currentVideoId);
            const progressData = result[STORAGE_PREFIX + currentVideoId];

            // Determine which timestamp to use
            let targetTime = null;
            let source = '';

            if (urlTimestamp !== null && urlTimestamp > 0) {
                targetTime = urlTimestamp;
                source = 'URL parameter';
                console.log(`Found URL timestamp for ${currentVideoId}: ${Math.floor(targetTime)}s`);
            } else if (progressData && progressData.timestamp) {
                targetTime = progressData.timestamp;
                source = 'saved progress';
                console.log(`Found saved progress for ${currentVideoId}: ${Math.floor(targetTime)}s`);
            }

            if (targetTime !== null) {
                // Wait for video to be ready and attempt multiple restore methods
                const restoreProgress = (attempts = 0) => {
                    if (attempts > 20) { // Max 10 seconds of attempts
                        console.log(`Failed to restore ${source} after multiple attempts`);
                        return;
                    }

                    if (videoElement && videoElement.readyState >= 2 && videoElement.duration > 0) {
                        // Try multiple methods to ensure the seek works
                        videoElement.currentTime = targetTime;

                        // Also try seeking after a small delay
                        setTimeout(() => {
                            if (Math.abs(videoElement.currentTime - targetTime) > 2) {
                                videoElement.currentTime = targetTime;
                                console.log(`✅ Restored ${source} to ${Math.floor(targetTime)}s (retry)`);
                            } else {
                                console.log(`✅ Restored ${source} to ${Math.floor(targetTime)}s`);
                            }
                        }, 100);

                        // Listen for loadeddata event to ensure seek worked
                        const onLoadedData = () => {
                            if (Math.abs(videoElement.currentTime - targetTime) > 2) {
                                videoElement.currentTime = targetTime;
                                console.log(`✅ Restored ${source} to ${Math.floor(targetTime)}s (on loadeddata)`);
                            }
                            videoElement.removeEventListener('loadeddata', onLoadedData);
                        };
                        videoElement.addEventListener('loadeddata', onLoadedData);

                    } else {
                        setTimeout(() => restoreProgress(attempts + 1), 500);
                    }
                };

                restoreProgress();
            } else {
                console.log(`No saved progress or URL timestamp found for ${currentVideoId}`);
            }
        } catch (error) {
            console.error('Failed to load progress:', error);
        }
    }

    // Save crash recovery data
    async function saveCrashRecoveryData() {
        if (!currentVideoId || !videoElement) return;

        const crashData = {
            videoId: currentVideoId,
            url: window.location.href,
            timestamp: videoElement.currentTime,
            title: document.title,
            lastActive: Date.now()
        };

        try {
            await chrome.runtime.sendMessage({
                action: 'saveCrashRecovery',
                data: crashData
            });
        } catch (error) {
            // Ignore errors if extension context is invalidated
        }
    }

    // Check for crash recovery
    async function checkCrashRecovery() {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getCrashRecovery'
            });

            if (response && response.videoId && response.lastActive) {
                const timeSinceLastActive = Date.now() - response.lastActive;
                const fiveMinutes = 5 * 60 * 1000;

                // If last activity was within 5 minutes and it's a different video
                if (timeSinceLastActive < fiveMinutes && response.videoId !== currentVideoId) {
                    console.log('🔄 Crash recovery available:', response);

                    // Show recovery option (you could make this a notification or popup)
                    if (confirm(`It looks like your browser crashed while watching a video. Would you like to resume "${response.title}" from ${Math.floor(response.timestamp)}s?`)) {
                        // Navigate to the crashed video with timestamp
                        const recoveryUrl = `https://www.youtube.com/watch?v=${response.videoId}&t=${Math.floor(response.timestamp)}s`;
                        window.location.href = recoveryUrl;
                        return true;
                    }
                }
            }
        } catch (error) {
            // Ignore errors if extension context is invalidated
        }
        return false;
    }

    // Start tracking progress
    function startTracking() {
        if (saveInterval) {
            clearInterval(saveInterval);
        }
        
        saveInterval = setInterval(saveProgress, SAVE_INTERVAL_MS);
        
        // Save progress when user leaves the page
        window.addEventListener('beforeunload', saveProgress);
        
        // Save progress when video is paused
        if (videoElement) {
            videoElement.addEventListener('pause', saveProgress);
        }
    }
    
    // Stop tracking progress
    function stopTracking() {
        if (saveInterval) {
            clearInterval(saveInterval);
            saveInterval = null;
        }
    }
    
    // Initialize the extension for current video
    async function initializeForVideo() {
        const videoId = getVideoId();

        if (!videoId) {
            console.log('No video ID found, stopping tracking');
            stopTracking();
            currentVideoId = null;
            isInitialized = false;
            return;
        }

        if (videoId === currentVideoId && isInitialized) {
            console.log(`Already initialized for video: ${videoId}`);
            return; // Already initialized for this video
        }

        console.log(`Initializing for video: ${videoId}`);

        // Reset state for new video
        stopTracking();
        currentVideoId = videoId;
        videoElement = getVideoElement();
        isInitialized = false;

        if (!videoElement) {
            console.log('Video element not found, retrying...');
            setTimeout(initializeForVideo, 1000);
            return;
        }

        console.log('Video element found, waiting for it to be ready...');

        // Wait for video to be ready before loading progress
        const waitForVideoReady = () => {
            if (videoElement.readyState >= 2 && videoElement.duration > 0) {
                console.log('Video is ready, loading saved progress...');
                loadProgress().then(() => {
                    // Start tracking after progress is loaded
                    startTracking();
                    isInitialized = true;
                    console.log('YouTube Progress Saver initialized successfully');
                });
            } else {
                console.log('Video not ready yet, waiting...');
                setTimeout(waitForVideoReady, 500);
            }
        };

        waitForVideoReady();
    }
    
    // Handle YouTube's SPA navigation
    function handleNavigation() {
        // YouTube uses pushState for navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function() {
            originalPushState.apply(history, arguments);
            setTimeout(initializeForVideo, 1000);
        };
        
        history.replaceState = function() {
            originalReplaceState.apply(history, arguments);
            setTimeout(initializeForVideo, 1000);
        };
        
        // Also listen for popstate
        window.addEventListener('popstate', () => {
            setTimeout(initializeForVideo, 1000);
        });
    }
    
    // Wait for page to be ready
    function waitForPageReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeForVideo);
        } else {
            initializeForVideo();
        }
    }
    
    // Initialize everything
    async function init() {
        console.log('YouTube Progress Saver content script loaded');

        // Check for crash recovery first (only on YouTube homepage or video pages)
        if (window.location.pathname === '/' || window.location.pathname === '/watch') {
            const recovered = await checkCrashRecovery();
            if (recovered) {
                return; // Don't continue initialization if we're navigating to recovery video
            }
        }

        handleNavigation();
        waitForPageReady();

        // Also try to initialize after a short delay
        setTimeout(initializeForVideo, 2000);
    }
    
    // Start the extension
    init();
    
})();
