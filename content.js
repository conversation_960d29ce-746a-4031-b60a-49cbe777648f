// YouTube Watch Progress Saver - Content Script
(function() {
    'use strict';
    
    let currentVideoId = null;
    let videoElement = null;
    let saveInterval = null;
    let isInitialized = false;
    
    // Configuration
    const SAVE_INTERVAL_MS = 5000; // Save every 5 seconds
    const MIN_WATCH_TIME = 10; // Minimum seconds before saving progress
    const STORAGE_PREFIX = 'yt_progress_';
    
    // Extract video ID from YouTube URL
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }
    
    // Get the video element
    function getVideoElement() {
        return document.querySelector('video');
    }
    
    // Save current progress to storage
    async function saveProgress() {
        if (!videoElement || !currentVideoId) return;
        
        const currentTime = videoElement.currentTime;
        const duration = videoElement.duration;
        
        // Don't save if video just started or is almost finished
        if (currentTime < MIN_WATCH_TIME || currentTime > duration - 30) {
            return;
        }
        
        const progressData = {
            videoId: currentVideoId,
            timestamp: currentTime,
            duration: duration,
            lastSaved: Date.now(),
            url: window.location.href
        };
        
        try {
            await chrome.storage.local.set({
                [STORAGE_PREFIX + currentVideoId]: progressData
            });
            console.log(`Progress saved for ${currentVideoId}: ${Math.floor(currentTime)}s`);
        } catch (error) {
            console.error('Failed to save progress:', error);
        }
    }
    
    // Load and restore progress
    async function loadProgress() {
        if (!currentVideoId) return;
        
        try {
            const result = await chrome.storage.local.get(STORAGE_PREFIX + currentVideoId);
            const progressData = result[STORAGE_PREFIX + currentVideoId];
            
            if (progressData && progressData.timestamp) {
                const savedTime = progressData.timestamp;
                console.log(`Found saved progress for ${currentVideoId}: ${Math.floor(savedTime)}s`);
                
                // Wait for video to be ready
                const restoreProgress = () => {
                    if (videoElement && videoElement.readyState >= 2) {
                        videoElement.currentTime = savedTime;
                        console.log(`Restored progress to ${Math.floor(savedTime)}s`);
                    } else {
                        setTimeout(restoreProgress, 500);
                    }
                };
                
                restoreProgress();
            }
        } catch (error) {
            console.error('Failed to load progress:', error);
        }
    }
    
    // Start tracking progress
    function startTracking() {
        if (saveInterval) {
            clearInterval(saveInterval);
        }
        
        saveInterval = setInterval(saveProgress, SAVE_INTERVAL_MS);
        
        // Save progress when user leaves the page
        window.addEventListener('beforeunload', saveProgress);
        
        // Save progress when video is paused
        if (videoElement) {
            videoElement.addEventListener('pause', saveProgress);
        }
    }
    
    // Stop tracking progress
    function stopTracking() {
        if (saveInterval) {
            clearInterval(saveInterval);
            saveInterval = null;
        }
    }
    
    // Initialize the extension for current video
    async function initializeForVideo() {
        const videoId = getVideoId();
        
        if (!videoId) {
            console.log('No video ID found, stopping tracking');
            stopTracking();
            return;
        }
        
        if (videoId === currentVideoId && isInitialized) {
            return; // Already initialized for this video
        }
        
        console.log(`Initializing for video: ${videoId}`);
        currentVideoId = videoId;
        videoElement = getVideoElement();
        
        if (!videoElement) {
            console.log('Video element not found, retrying...');
            setTimeout(initializeForVideo, 1000);
            return;
        }
        
        // Load saved progress
        await loadProgress();
        
        // Start tracking
        startTracking();
        isInitialized = true;
        
        console.log('YouTube Progress Saver initialized');
    }
    
    // Handle YouTube's SPA navigation
    function handleNavigation() {
        // YouTube uses pushState for navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function() {
            originalPushState.apply(history, arguments);
            setTimeout(initializeForVideo, 1000);
        };
        
        history.replaceState = function() {
            originalReplaceState.apply(history, arguments);
            setTimeout(initializeForVideo, 1000);
        };
        
        // Also listen for popstate
        window.addEventListener('popstate', () => {
            setTimeout(initializeForVideo, 1000);
        });
    }
    
    // Wait for page to be ready
    function waitForPageReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeForVideo);
        } else {
            initializeForVideo();
        }
    }
    
    // Initialize everything
    function init() {
        console.log('YouTube Progress Saver content script loaded');
        handleNavigation();
        waitForPageReady();
        
        // Also try to initialize after a short delay
        setTimeout(initializeForVideo, 2000);
    }
    
    // Start the extension
    init();
    
})();
