// YouTube Watch Progress Saver - Content Script
(function() {
    'use strict';
    
    let currentVideoId = null;
    let previousVideoId = null; // Track the previous video to detect changes
    let videoElement = null;
    let saveInterval = null;
    let isInitialized = false;
    let crashRecoveryShown = false; // Track if crash recovery was already shown this session
    let extensionContextValid = true; // Track if extension context is still valid
    
    // Configuration
    const SAVE_INTERVAL_MS = 5000; // Save every 5 seconds
    const MIN_WATCH_TIME = 10; // Minimum seconds before saving progress
    const STORAGE_PREFIX = 'yt_progress_';
    const COMPLETION_THRESHOLD = 30; // Seconds from end to consider video "completed"
    
    // Extract video ID from YouTube URL
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }

    // Extract timestamp from YouTube URL (t parameter)
    function getUrlTimestamp() {
        const urlParams = new URLSearchParams(window.location.search);
        const tParam = urlParams.get('t');
        if (tParam) {
            // Handle formats like "89s", "1m30s", "90"
            if (tParam.includes('m') || tParam.includes('s')) {
                let seconds = 0;
                const minutesMatch = tParam.match(/(\d+)m/);
                const secondsMatch = tParam.match(/(\d+)s/);
                if (minutesMatch) seconds += parseInt(minutesMatch[1]) * 60;
                if (secondsMatch) seconds += parseInt(secondsMatch[1]);
                return seconds;
            } else {
                // Just a number in seconds
                return parseInt(tParam);
            }
        }
        return null;
    }
    
    // Get the video element
    function getVideoElement() {
        return document.querySelector('video');
    }
    
    // Check if extension context is still valid
    function isExtensionContextValid() {
        try {
            // More robust check - test if we can actually access chrome.runtime
            if (!chrome || !chrome.runtime) {
                extensionContextValid = false;
                return false;
            }

            // Try to access runtime.id - this will throw if context is invalid
            const runtimeId = chrome.runtime.id;
            if (!runtimeId) {
                extensionContextValid = false;
                return false;
            }

            return extensionContextValid;
        } catch (error) {
            // Only mark as invalid if it's actually a context invalidation error
            if (error.message && error.message.includes('Extension context invalidated')) {
                extensionContextValid = false;
                return false;
            }
            // For other errors, assume context is still valid
            return extensionContextValid;
        }
    }

    // Test extension context by trying to use it
    async function testExtensionContext() {
        try {
            // Try a simple storage operation to test if context is valid
            await chrome.storage.local.get('test_context_key');
            return true;
        } catch (error) {
            if (error.message && error.message.includes('Extension context invalidated')) {
                extensionContextValid = false;
                return false;
            }
            // For other errors, assume context is still valid
            return true;
        }
    }

    // Save current progress to storage
    async function saveProgress() {
        if (!videoElement || !currentVideoId) {
            console.log('Cannot save progress: missing video element or video ID');
            return;
        }

        // Only perform expensive context check if we've had previous errors
        if (!extensionContextValid) {
            const contextValid = await testExtensionContext();
            if (!contextValid) {
                console.log('⚠️ Extension context invalid - stopping progress tracking');
                stopTracking();
                return;
            }
            // Context is valid again, reset flag
            extensionContextValid = true;
        }

        const currentTime = videoElement.currentTime;
        const duration = videoElement.duration;

        console.log(`Attempting to save progress: ${Math.floor(currentTime)}s / ${Math.floor(duration)}s`);

        // Don't save if video just started or is almost finished
        if (currentTime < MIN_WATCH_TIME) {
            console.log(`Not saving: video time ${currentTime}s is less than minimum ${MIN_WATCH_TIME}s`);
            return;
        }

        if (currentTime > duration - COMPLETION_THRESHOLD) {
            console.log(`Video is almost finished (${Math.floor(duration - currentTime)}s remaining)`);
            // Remove progress for completed video
            await removeCompletedProgress();
            return;
        }

        const progressData = {
            videoId: currentVideoId,
            timestamp: currentTime,
            duration: duration,
            lastSaved: Date.now(),
            url: window.location.href
        };

        try {
            await chrome.storage.local.set({
                [STORAGE_PREFIX + currentVideoId]: progressData
            });
            console.log(`✅ Progress saved for ${currentVideoId}: ${Math.floor(currentTime)}s / ${Math.floor(duration)}s`);

            // Also save for crash recovery
            saveCrashRecoveryData();
        } catch (error) {
            // Handle extension context invalidated error gracefully
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.log('⚠️ Extension context invalidated - progress saving paused');
                extensionContextValid = false;
                stopTracking();
                return;
            }
            if (error.message && error.message.includes('Receiving end does not exist')) {
                console.log('⚠️ Background script not ready - progress saving skipped');
                return;
            }
            console.error('❌ Failed to save progress:', error);
        }
    }
    
    // Load and restore progress
    async function loadProgress() {
        if (!currentVideoId) return;

        try {
            // Check for URL timestamp first (takes priority)
            const urlTimestamp = getUrlTimestamp();

            // Get saved progress
            const result = await chrome.storage.local.get(STORAGE_PREFIX + currentVideoId);
            const progressData = result[STORAGE_PREFIX + currentVideoId];

            // Determine which timestamp to use
            let targetTime = null;
            let source = '';

            if (urlTimestamp !== null && urlTimestamp > 0) {
                targetTime = urlTimestamp;
                source = 'URL parameter';
                console.log(`Found URL timestamp for ${currentVideoId}: ${Math.floor(targetTime)}s`);
            } else if (progressData && progressData.timestamp) {
                targetTime = progressData.timestamp;
                source = 'saved progress';
                console.log(`Found saved progress for ${currentVideoId}: ${Math.floor(targetTime)}s`);
            }

            if (targetTime !== null) {
                // Wait for video to be ready and attempt multiple restore methods
                const restoreProgress = (attempts = 0) => {
                    if (attempts > 20) { // Max 10 seconds of attempts
                        console.log(`Failed to restore ${source} after multiple attempts`);
                        return;
                    }

                    if (videoElement && videoElement.readyState >= 2 && videoElement.duration > 0) {
                        // Try multiple methods to ensure the seek works
                        videoElement.currentTime = targetTime;

                        // Also try seeking after a small delay
                        setTimeout(() => {
                            if (Math.abs(videoElement.currentTime - targetTime) > 2) {
                                videoElement.currentTime = targetTime;
                                console.log(`✅ Restored ${source} to ${Math.floor(targetTime)}s (retry)`);
                            } else {
                                console.log(`✅ Restored ${source} to ${Math.floor(targetTime)}s`);
                            }
                        }, 100);

                        // Listen for loadeddata event to ensure seek worked
                        const onLoadedData = () => {
                            if (Math.abs(videoElement.currentTime - targetTime) > 2) {
                                videoElement.currentTime = targetTime;
                                console.log(`✅ Restored ${source} to ${Math.floor(targetTime)}s (on loadeddata)`);
                            }
                            videoElement.removeEventListener('loadeddata', onLoadedData);
                        };
                        videoElement.addEventListener('loadeddata', onLoadedData);

                    } else {
                        setTimeout(() => restoreProgress(attempts + 1), 500);
                    }
                };

                restoreProgress();
            } else {
                console.log(`No saved progress or URL timestamp found for ${currentVideoId}`);
            }
        } catch (error) {
            console.error('Failed to load progress:', error);
        }
    }

    // Clear all saved progress except current video
    async function clearOldProgress() {
        try {
            const allData = await chrome.storage.local.get(null);
            const keysToRemove = [];

            for (const key of Object.keys(allData)) {
                if (key.startsWith(STORAGE_PREFIX) &&
                    key !== STORAGE_PREFIX + currentVideoId &&
                    key !== 'yt_progress_enabled' &&
                    key !== 'yt_progress_save_interval' &&
                    key !== 'yt_progress_min_watch_time' &&
                    key !== 'yt_progress_crash_recovery' &&
                    key !== 'yt_progress_single_video_mode' &&
                    key !== 'yt_progress_last_video') {
                    keysToRemove.push(key);
                }
            }

            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log(`🧹 Cleared ${keysToRemove.length} old progress entries (single video mode)`);
            }
        } catch (error) {
            console.error('Failed to clear old progress:', error);
        }
    }

    // Remove progress for completed video
    async function removeCompletedProgress() {
        if (!currentVideoId) return;

        try {
            await chrome.storage.local.remove(STORAGE_PREFIX + currentVideoId);
            console.log(`🎬 Removed completed progress for ${currentVideoId}`);
        } catch (error) {
            console.error('Failed to remove completed progress:', error);
        }
    }

    // Remove progress for previous video when switching
    async function removePreviousVideoProgress() {
        if (!previousVideoId || previousVideoId === currentVideoId) return;

        // Check if auto-cleanup is enabled
        try {
            const settings = await chrome.storage.local.get('yt_progress_auto_cleanup');
            const autoCleanup = settings.yt_progress_auto_cleanup !== false; // Default to true

            if (!autoCleanup) {
                console.log(`🔄 Auto-cleanup disabled - keeping progress for ${previousVideoId}`);
                return;
            }
        } catch (error) {
            console.error('Failed to check auto-cleanup setting:', error);
            // Continue with cleanup if setting check fails
        }

        try {
            await chrome.storage.local.remove(STORAGE_PREFIX + previousVideoId);
            console.log(`🔄 Auto-cleanup: Removed previous video progress: ${previousVideoId} (switched to ${currentVideoId})`);
        } catch (error) {
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.log('⚠️ Extension context invalidated - previous video cleanup skipped');
                return;
            }
            console.error('Failed to remove previous video progress:', error);
        }
    }

    // Check if video is near completion
    function isVideoNearCompletion() {
        if (!videoElement || !videoElement.duration) return false;
        const timeRemaining = videoElement.duration - videoElement.currentTime;
        return timeRemaining <= COMPLETION_THRESHOLD;
    }

    // Save crash recovery data
    async function saveCrashRecoveryData() {
        if (!currentVideoId || !videoElement) {
            console.log('🔄 Cannot save crash recovery: missing video ID or element');
            return;
        }

        const crashData = {
            videoId: currentVideoId,
            url: window.location.href,
            timestamp: videoElement.currentTime,
            title: document.title,
            lastActive: Date.now()
        };

        try {
            await chrome.runtime.sendMessage({
                action: 'saveCrashRecovery',
                data: crashData
            });
            console.log('🔄 Crash recovery data saved:', crashData);
        } catch (error) {
            // Handle extension context invalidated error gracefully
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.log('⚠️ Extension context invalidated - crash recovery saving paused');
                return;
            }
            if (error.message && error.message.includes('Receiving end does not exist')) {
                console.log('⚠️ Background script not ready - crash recovery saving skipped');
                return;
            }
            console.error('🔄 Failed to save crash recovery data:', error);
        }
    }

    // Check for crash recovery
    async function checkCrashRecovery() {
        // Only show crash recovery once per session
        if (crashRecoveryShown) {
            console.log('🔄 Crash recovery already shown this session');
            return false;
        }

        try {
            console.log('🔄 Requesting crash recovery data...');
            const response = await chrome.runtime.sendMessage({
                action: 'getCrashRecovery'
            });

            console.log('🔄 Crash recovery response:', response);

            if (response && response.videoId && response.lastActive) {
                const timeSinceLastActive = Date.now() - response.lastActive;
                const fiveMinutes = 5 * 60 * 1000;

                console.log(`🔄 Time since last active: ${Math.floor(timeSinceLastActive / 1000)}s`);
                console.log(`🔄 Current video ID: ${currentVideoId}, Crashed video ID: ${response.videoId}`);

                // If last activity was within 5 minutes and it's a different video
                if (timeSinceLastActive < fiveMinutes && response.videoId !== currentVideoId) {
                    console.log('🔄 Crash recovery available:', response);
                    crashRecoveryShown = true; // Mark as shown

                    // Show recovery option
                    if (confirm(`It looks like your browser crashed while watching a video. Would you like to resume "${response.title}" from ${Math.floor(response.timestamp)}s?`)) {
                        // Clear the crash recovery data first
                        await clearCrashRecoveryData();

                        // Navigate to the crashed video with timestamp
                        const recoveryUrl = `https://www.youtube.com/watch?v=${response.videoId}&t=${Math.floor(response.timestamp)}s`;
                        console.log('🔄 Navigating to recovery URL:', recoveryUrl);
                        window.location.href = recoveryUrl;
                        return true;
                    } else {
                        // User declined recovery, clear the data
                        await clearCrashRecoveryData();
                        console.log('🔄 Crash recovery declined, data cleared');
                    }
                } else if (timeSinceLastActive >= fiveMinutes) {
                    // Old crash data, clear it automatically
                    await clearCrashRecoveryData();
                    console.log('🔄 Old crash recovery data cleared (>5 minutes)');
                } else {
                    console.log('🔄 Crash recovery not applicable (same video or recent activity)');
                }
            } else {
                console.log('🔄 No crash recovery data found');
            }
        } catch (error) {
            // Handle extension context invalidated error gracefully
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.log('⚠️ Extension context invalidated - crash recovery check skipped');
                return false;
            }
            if (error.message && error.message.includes('Receiving end does not exist')) {
                console.log('⚠️ Background script not ready - crash recovery check skipped');
                return false;
            }
            console.error('🔄 Crash recovery check failed:', error);
        }
        return false;
    }

    // Clear crash recovery data
    async function clearCrashRecoveryData() {
        try {
            await chrome.runtime.sendMessage({
                action: 'clearCrashRecovery'
            });
        } catch (error) {
            // Handle extension context invalidated error gracefully
            if (error.message && error.message.includes('Extension context invalidated')) {
                console.log('⚠️ Extension context invalidated - crash recovery clear skipped');
                return;
            }
            if (error.message && error.message.includes('Receiving end does not exist')) {
                console.log('⚠️ Background script not ready - crash recovery clear skipped');
                return;
            }
            console.error('🔄 Failed to clear crash recovery data:', error);
        }
    }

    // Start tracking progress
    function startTracking() {
        if (saveInterval) {
            clearInterval(saveInterval);
        }

        saveInterval = setInterval(saveProgress, SAVE_INTERVAL_MS);

        // Save progress when user leaves the page
        window.addEventListener('beforeunload', saveProgress);

        // Save progress when video is paused
        if (videoElement) {
            videoElement.addEventListener('pause', saveProgress);
        }

        // Periodic context health check (every 30 seconds)
        setInterval(async () => {
            if (!extensionContextValid) {
                const contextValid = await testExtensionContext();
                if (contextValid) {
                    console.log('✅ Extension context restored');
                    extensionContextValid = true;
                }
            }
        }, 30000);
    }
    
    // Stop tracking progress
    function stopTracking() {
        if (saveInterval) {
            clearInterval(saveInterval);
            saveInterval = null;
        }
    }
    
    // Initialize the extension for current video
    async function initializeForVideo() {
        const videoId = getVideoId();

        if (!videoId) {
            console.log('No video ID found, stopping tracking');
            stopTracking();
            currentVideoId = null;
            isInitialized = false;
            return;
        }

        if (videoId === currentVideoId && isInitialized) {
            console.log(`Already initialized for video: ${videoId}`);
            return; // Already initialized for this video
        }

        console.log(`Initializing for video: ${videoId}`);

        // Detect video change and clean up previous video
        if (currentVideoId && currentVideoId !== videoId) {
            console.log(`📹 Video changed from ${currentVideoId} to ${videoId}`);
            previousVideoId = currentVideoId;

            // Remove previous video progress
            await removePreviousVideoProgress();
        }

        // Reset state for new video
        stopTracking();
        currentVideoId = videoId;
        videoElement = getVideoElement();
        isInitialized = false;

        if (!videoElement) {
            console.log('Video element not found, retrying...');
            setTimeout(initializeForVideo, 1000);
            return;
        }

        console.log('Video element found, waiting for it to be ready...');

        // Wait for video to be ready before loading progress
        const waitForVideoReady = async () => {
            if (videoElement.readyState >= 2 && videoElement.duration > 0) {
                console.log('Video is ready, checking settings and loading progress...');

                // Check if single video mode is enabled
                try {
                    const settings = await chrome.storage.local.get('yt_progress_single_video_mode');
                    const singleVideoMode = settings.yt_progress_single_video_mode !== false; // Default to true

                    if (singleVideoMode) {
                        console.log('🎯 Single video mode enabled, clearing old progress...');
                        await clearOldProgress();
                    }
                } catch (error) {
                    console.error('Failed to check settings:', error);
                }

                await loadProgress();

                // Start tracking after progress is loaded
                startTracking();
                isInitialized = true;
                console.log('YouTube Progress Saver initialized successfully');
            } else {
                console.log('Video not ready yet, waiting...');
                setTimeout(waitForVideoReady, 500);
            }
        };

        waitForVideoReady();
    }
    
    // Handle YouTube's SPA navigation
    function handleNavigation() {
        // YouTube uses pushState for navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function() {
            originalPushState.apply(history, arguments);
            setTimeout(initializeForVideo, 1000);
        };
        
        history.replaceState = function() {
            originalReplaceState.apply(history, arguments);
            setTimeout(initializeForVideo, 1000);
        };
        
        // Also listen for popstate
        window.addEventListener('popstate', () => {
            setTimeout(initializeForVideo, 1000);
        });
    }
    
    // Wait for page to be ready
    function waitForPageReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeForVideo);
        } else {
            initializeForVideo();
        }
    }
    
    // Initialize everything
    async function init() {
        console.log('YouTube Progress Saver content script loaded');

        // Check for crash recovery first (on any YouTube page)
        console.log('🔍 Checking for crash recovery on:', window.location.pathname);
        const recovered = await checkCrashRecovery();
        if (recovered) {
            return; // Don't continue initialization if we're navigating to recovery video
        }

        handleNavigation();
        waitForPageReady();

        // Also try to initialize after a short delay
        setTimeout(initializeForVideo, 2000);
    }
    
    // Start the extension
    init();
    
})();
