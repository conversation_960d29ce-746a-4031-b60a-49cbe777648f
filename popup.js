// YouTube Watch Progress Saver - Popup Script

document.addEventListener('DOMContentLoaded', async function() {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const enabledToggle = document.getElementById('enabledToggle');
    const crashRecoveryToggle = document.getElementById('crashRecoveryToggle');
    const saveInterval = document.getElementById('saveInterval');
    const videoCount = document.getElementById('videoCount');
    const storageUsed = document.getElementById('storageUsed');
    const clearDataBtn = document.getElementById('clearData');
    const exportDataBtn = document.getElementById('exportData');
    
    // Load current settings and stats
    await loadSettings();
    await loadStats();
    await checkStatus();
    
    // Event listeners
    enabledToggle.addEventListener('change', saveSettings);
    crashRecoveryToggle.addEventListener('change', saveSettings);
    saveInterval.addEventListener('change', saveSettings);
    clearDataBtn.addEventListener('click', clearAllData);
    exportDataBtn.addEventListener('click', exportData);
    
    async function loadSettings() {
        try {
            const settings = await chrome.storage.local.get([
                'yt_progress_enabled',
                'yt_progress_save_interval',
                'yt_progress_crash_recovery'
            ]);

            enabledToggle.checked = settings.yt_progress_enabled !== false;
            crashRecoveryToggle.checked = settings.yt_progress_crash_recovery !== false;

            const intervalSeconds = (settings.yt_progress_save_interval || 5000) / 1000;
            saveInterval.value = intervalSeconds.toString();
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
    
    async function saveSettings() {
        try {
            const settings = {
                yt_progress_enabled: enabledToggle.checked,
                yt_progress_crash_recovery: crashRecoveryToggle.checked,
                yt_progress_save_interval: parseInt(saveInterval.value) * 1000
            };

            await chrome.storage.local.set(settings);
            console.log('Settings saved:', settings);

            // Update status
            await checkStatus();
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }
    
    async function loadStats() {
        try {
            const allData = await chrome.storage.local.get(null);
            
            // Count videos with saved progress
            let videoProgressCount = 0;
            let totalSize = 0;
            
            for (const [key, value] of Object.entries(allData)) {
                if (key.startsWith('yt_progress_') && key !== 'yt_progress_enabled' && key !== 'yt_progress_save_interval') {
                    videoProgressCount++;
                    totalSize += JSON.stringify(value).length;
                }
            }
            
            videoCount.textContent = videoProgressCount;
            storageUsed.textContent = formatBytes(totalSize);
            
        } catch (error) {
            console.error('Failed to load stats:', error);
            videoCount.textContent = 'Error';
            storageUsed.textContent = 'Error';
        }
    }
    
    async function checkStatus() {
        try {
            // Check if we're on a YouTube tab
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentTab = tabs[0];
            
            if (currentTab && currentTab.url && currentTab.url.includes('youtube.com')) {
                const settings = await chrome.storage.local.get('yt_progress_enabled');
                const isEnabled = settings.yt_progress_enabled !== false;
                
                if (isEnabled) {
                    statusIndicator.className = 'status-indicator active';
                    statusText.textContent = 'Active on YouTube';
                } else {
                    statusIndicator.className = 'status-indicator disabled';
                    statusText.textContent = 'Disabled';
                }
            } else {
                statusIndicator.className = 'status-indicator inactive';
                statusText.textContent = 'Not on YouTube';
            }
        } catch (error) {
            console.error('Failed to check status:', error);
            statusIndicator.className = 'status-indicator error';
            statusText.textContent = 'Error';
        }
    }
    
    async function clearAllData() {
        if (!confirm('Are you sure you want to clear all saved progress data? This cannot be undone.')) {
            return;
        }
        
        try {
            const allData = await chrome.storage.local.get(null);
            const keysToRemove = [];
            
            for (const key of Object.keys(allData)) {
                if (key.startsWith('yt_progress_') && key !== 'yt_progress_enabled' && key !== 'yt_progress_save_interval') {
                    keysToRemove.push(key);
                }
            }
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log(`Cleared ${keysToRemove.length} progress entries`);
            }
            
            await loadStats();
            alert('All progress data has been cleared.');
            
        } catch (error) {
            console.error('Failed to clear data:', error);
            alert('Failed to clear data. Please try again.');
        }
    }
    
    async function exportData() {
        try {
            const allData = await chrome.storage.local.get(null);
            const progressData = {};
            
            for (const [key, value] of Object.entries(allData)) {
                if (key.startsWith('yt_progress_') && key !== 'yt_progress_enabled' && key !== 'yt_progress_save_interval') {
                    progressData[key] = value;
                }
            }
            
            const dataStr = JSON.stringify(progressData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const url = URL.createObjectURL(dataBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `youtube-progress-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('Failed to export data:', error);
            alert('Failed to export data. Please try again.');
        }
    }
    
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
