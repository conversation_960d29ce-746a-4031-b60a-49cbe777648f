// YouTube Watch Progress Saver - Popup Script

document.addEventListener('DOMContentLoaded', async function() {
    // UI Elements
    const statusIndicator = document.getElementById('statusIndicator');
    const statusText = document.getElementById('statusText');
    const enabledToggle = document.getElementById('enabledToggle');
    const crashRecoveryToggle = document.getElementById('crashRecoveryToggle');
    const singleVideoToggle = document.getElementById('singleVideoToggle');
    const saveIntervalSelect = document.getElementById('saveIntervalSelect');
    const videoCount = document.getElementById('videoCount');
    const storageUsed = document.getElementById('storageUsed');
    const clearDataBtn = document.getElementById('clearData');
    const clearCurrentDataBtn = document.getElementById('clearCurrentData');
    const exportDataBtn = document.getElementById('exportData');

    // Performance monitoring elements
    const memoryUsage = document.getElementById('memoryUsage');
    const cpuUsage = document.getElementById('cpuUsage');
    const performanceIndicator = document.getElementById('performanceIndicator');
    const footerStatus = document.getElementById('footerStatus');
    
    // Load current settings and stats
    await loadSettings();
    await loadStats();
    await checkStatus();
    await loadPerformanceStats();

    // Event listeners
    enabledToggle.addEventListener('change', saveSettings);
    crashRecoveryToggle.addEventListener('change', saveSettings);
    singleVideoToggle.addEventListener('change', saveSettings);
    saveIntervalSelect.addEventListener('change', saveSettings);
    clearDataBtn.addEventListener('click', clearAllData);
    clearCurrentDataBtn.addEventListener('click', clearCurrentVideo);
    exportDataBtn.addEventListener('click', exportData);

    // Start performance monitoring
    setInterval(loadPerformanceStats, 3000); // Update every 3 seconds
    
    async function loadSettings() {
        try {
            const settings = await chrome.storage.local.get([
                'yt_progress_enabled',
                'yt_progress_save_interval',
                'yt_progress_crash_recovery',
                'yt_progress_single_video_mode'
            ]);

            enabledToggle.checked = settings.yt_progress_enabled !== false;
            crashRecoveryToggle.checked = settings.yt_progress_crash_recovery !== false;
            singleVideoToggle.checked = settings.yt_progress_single_video_mode !== false;

            const intervalSeconds = (settings.yt_progress_save_interval || 5000) / 1000;
            saveIntervalSelect.value = intervalSeconds.toString();

            // Update the quick stat display
            document.getElementById('saveInterval').textContent = intervalSeconds + 's';
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    }
    
    async function saveSettings() {
        try {
            const settings = {
                yt_progress_enabled: enabledToggle.checked,
                yt_progress_crash_recovery: crashRecoveryToggle.checked,
                yt_progress_single_video_mode: singleVideoToggle.checked,
                yt_progress_save_interval: parseInt(saveIntervalSelect.value) * 1000
            };

            await chrome.storage.local.set(settings);
            console.log('Settings saved:', settings);

            // Update status and quick stats
            await checkStatus();
            document.getElementById('saveInterval').textContent = parseInt(saveIntervalSelect.value) + 's';
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    async function loadPerformanceStats() {
        try {
            // Get memory usage from Chrome's memory API
            if (chrome.system && chrome.system.memory) {
                const memInfo = await chrome.system.memory.getInfo();
                const usedMemory = ((memInfo.capacity - memInfo.availableCapacity) / 1024 / 1024 / 1024).toFixed(1);
                const totalMemory = (memInfo.capacity / 1024 / 1024 / 1024).toFixed(1);
                memoryUsage.textContent = `${usedMemory}/${totalMemory}GB`;

                // Update performance indicator based on memory usage
                const memoryPercentage = (memInfo.capacity - memInfo.availableCapacity) / memInfo.capacity;
                updatePerformanceIndicator(memoryPercentage);
            } else {
                // Fallback: estimate extension memory usage
                const storageEstimate = await navigator.storage.estimate();
                const usedMB = (storageEstimate.usage / 1024 / 1024).toFixed(1);
                memoryUsage.textContent = `~${usedMB}MB`;

                // Set CPU usage as low for extension
                cpuUsage.textContent = 'Low';
                performanceIndicator.className = 'performance-indicator good';
            }

            // Update footer status
            footerStatus.textContent = 'Running smoothly';

        } catch (error) {
            console.error('Failed to load performance stats:', error);
            memoryUsage.textContent = 'N/A';
            cpuUsage.textContent = 'N/A';
            footerStatus.textContent = 'Performance data unavailable';
        }
    }

    function updatePerformanceIndicator(memoryPercentage) {
        if (memoryPercentage < 0.7) {
            performanceIndicator.className = 'performance-indicator good';
            footerStatus.textContent = 'Excellent performance';
        } else if (memoryPercentage < 0.85) {
            performanceIndicator.className = 'performance-indicator warning';
            footerStatus.textContent = 'Good performance';
        } else {
            performanceIndicator.className = 'performance-indicator critical';
            footerStatus.textContent = 'High memory usage';
        }
    }
    
    async function loadStats() {
        try {
            const allData = await chrome.storage.local.get(null);
            
            // Count videos with saved progress
            let videoProgressCount = 0;
            let totalSize = 0;
            
            for (const [key, value] of Object.entries(allData)) {
                if (key.startsWith('yt_progress_') && key !== 'yt_progress_enabled' && key !== 'yt_progress_save_interval') {
                    videoProgressCount++;
                    totalSize += JSON.stringify(value).length;
                }
            }
            
            videoCount.textContent = videoProgressCount;
            storageUsed.textContent = formatBytes(totalSize);
            
        } catch (error) {
            console.error('Failed to load stats:', error);
            videoCount.textContent = 'Error';
            storageUsed.textContent = 'Error';
        }
    }
    
    async function checkStatus() {
        try {
            // Check if we're on a YouTube tab
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentTab = tabs[0];
            
            if (currentTab && currentTab.url && currentTab.url.includes('youtube.com')) {
                const settings = await chrome.storage.local.get('yt_progress_enabled');
                const isEnabled = settings.yt_progress_enabled !== false;
                
                if (isEnabled) {
                    statusIndicator.className = 'status-indicator active';
                    statusText.textContent = 'Active on YouTube';
                } else {
                    statusIndicator.className = 'status-indicator disabled';
                    statusText.textContent = 'Disabled';
                }
            } else {
                statusIndicator.className = 'status-indicator inactive';
                statusText.textContent = 'Not on YouTube';
            }
        } catch (error) {
            console.error('Failed to check status:', error);
            statusIndicator.className = 'status-indicator error';
            statusText.textContent = 'Error';
        }
    }
    
    async function clearAllData() {
        if (!confirm('Are you sure you want to clear all saved progress data? This cannot be undone.')) {
            return;
        }
        
        try {
            const allData = await chrome.storage.local.get(null);
            const keysToRemove = [];
            
            for (const key of Object.keys(allData)) {
                if (key.startsWith('yt_progress_') && key !== 'yt_progress_enabled' && key !== 'yt_progress_save_interval') {
                    keysToRemove.push(key);
                }
            }
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log(`Cleared ${keysToRemove.length} progress entries`);
            }
            
            await loadStats();
            alert('All progress data has been cleared.');
            
        } catch (error) {
            console.error('Failed to clear data:', error);
            alert('Failed to clear data. Please try again.');
        }
    }

    async function clearCurrentVideo() {
        try {
            // Get current tab to find video ID
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentTab = tabs[0];

            if (currentTab && currentTab.url && currentTab.url.includes('youtube.com/watch')) {
                const url = new URL(currentTab.url);
                const videoId = url.searchParams.get('v');

                if (videoId) {
                    await chrome.storage.local.remove(`yt_progress_${videoId}`);
                    await loadStats();
                    alert(`Progress cleared for current video: ${videoId}`);
                } else {
                    alert('Could not find video ID in current tab.');
                }
            } else {
                alert('Please open a YouTube video tab first.');
            }
        } catch (error) {
            console.error('Failed to clear current video:', error);
            alert('Failed to clear current video progress.');
        }
    }

    async function exportData() {
        try {
            const allData = await chrome.storage.local.get(null);
            const progressData = {};
            
            for (const [key, value] of Object.entries(allData)) {
                if (key.startsWith('yt_progress_') && key !== 'yt_progress_enabled' && key !== 'yt_progress_save_interval') {
                    progressData[key] = value;
                }
            }
            
            const dataStr = JSON.stringify(progressData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const url = URL.createObjectURL(dataBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `youtube-progress-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('Failed to export data:', error);
            alert('Failed to export data. Please try again.');
        }
    }
    
    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
