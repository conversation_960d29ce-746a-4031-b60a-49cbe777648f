<!DOCTYPE html>
<html>
<head>
    <title>Test Crash Recovery</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .button { padding: 10px 20px; margin: 10px; background: #ff0000; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #cc0000; }
        .info { background: #f0f0f0; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>YouTube Progress Saver - Crash Recovery Test</h1>
    
    <div class="info">
        <h3>How to test crash recovery:</h3>
        <ol>
            <li>Open a YouTube video and watch for at least 15 seconds</li>
            <li>Use one of the buttons below to simulate a crash</li>
            <li>Go back to YouTube homepage or open a new YouTube tab</li>
            <li>You should see a recovery prompt</li>
        </ol>
    </div>
    
    <h3>Simulate Browser Crash:</h3>
    <button class="button" onclick="simulateTabCrash()">Close This Tab (Simulate Tab Crash)</button>
    <button class="button" onclick="simulateBrowserCrash()">Close Browser Window (Simulate Browser Crash)</button>
    
    <h3>Test URLs:</h3>
    <p>Try these YouTube URLs with timestamps to test URL parameter handling:</p>
    <ul>
        <li><a href="https://www.youtube.com/watch?v=YMS6yE_faEo&t=89s" target="_blank">Video with 89s timestamp</a></li>
        <li><a href="https://www.youtube.com/watch?v=YMS6yE_faEo&t=2m30s" target="_blank">Video with 2m30s timestamp</a></li>
        <li><a href="https://www.youtube.com/watch?v=YMS6yE_faEo&t=150" target="_blank">Video with 150s timestamp (number only)</a></li>
    </ul>
    
    <div class="info">
        <h3>Expected Behavior:</h3>
        <ul>
            <li><strong>URL Timestamps:</strong> Should take priority over saved progress</li>
            <li><strong>Saved Progress:</strong> Should resume where you left off if no URL timestamp</li>
            <li><strong>Crash Recovery:</strong> Should offer to resume crashed video within 5 minutes</li>
            <li><strong>Console Logs:</strong> Check browser console for detailed progress tracking</li>
        </ul>
    </div>
    
    <script>
        function simulateTabCrash() {
            alert('This will close the current tab to simulate a crash. Make sure you have a YouTube video playing in another tab first!');
            setTimeout(() => {
                window.close();
            }, 1000);
        }
        
        function simulateBrowserCrash() {
            alert('This will close the browser window. Make sure you have a YouTube video playing first!');
            setTimeout(() => {
                window.open('', '_self').close();
            }, 1000);
        }
    </script>
</body>
</html>
