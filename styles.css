/* YouTube Progress Saver - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    width: 320px;
    min-height: 400px;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.header {
    background: #ff0000;
    color: white;
    padding: 16px;
    text-align: center;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ccc;
}

.status-indicator.active {
    background: #4caf50;
    box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
}

.status-indicator.inactive {
    background: #ffc107;
}

.status-indicator.disabled {
    background: #f44336;
}

.status-indicator.error {
    background: #9e9e9e;
}

.content {
    flex: 1;
    padding: 16px;
}

.section {
    margin-bottom: 20px;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

.setting-item {
    margin-bottom: 12px;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 13px;
}

.setting-item input[type="checkbox"] {
    margin: 0;
}

.setting-item select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    margin-top: 4px;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #eee;
}

.stat-label {
    font-size: 13px;
    color: #666;
}

.stat-value {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #d32f2f;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.footer {
    padding: 12px 16px;
    background: white;
    border-top: 1px solid #eee;
    text-align: center;
    font-size: 11px;
    color: #666;
}

.footer p {
    margin-bottom: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
