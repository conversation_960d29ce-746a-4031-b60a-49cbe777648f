/* YouTube Progress Saver - Premium Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #1a1a1a;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 380px;
    min-height: 600px;
    overflow-x: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    margin: 8px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
    padding: 20px;
    border-radius: 16px 16px 0 0;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.logo-text h1 {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.subtitle {
    font-size: 12px;
    opacity: 0.8;
    font-weight: 400;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
    box-shadow: 0 0 8px rgba(74, 222, 128, 0.6);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-indicator.inactive {
    background: #fbbf24;
    box-shadow: 0 0 8px rgba(251, 191, 36, 0.6);
}

.status-indicator.disabled {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

.status-indicator.error {
    background: #6b7280;
    box-shadow: none;
    animation: none;
}

/* Performance Monitor */
.performance-section {
    padding: 16px 20px 0;
}

.performance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 16px;
    color: white;
    margin-bottom: 16px;
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.performance-title {
    font-weight: 600;
    font-size: 14px;
}

.performance-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
    box-shadow: 0 0 8px rgba(74, 222, 128, 0.8);
}

.performance-indicator.warning {
    background: #fbbf24;
    box-shadow: 0 0 8px rgba(251, 191, 36, 0.8);
}

.performance-indicator.critical {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.8);
}

.performance-stats {
    display: flex;
    justify-content: space-between;
}

.performance-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.performance-label {
    font-size: 11px;
    opacity: 0.8;
    margin-bottom: 4px;
}

.performance-value {
    font-size: 14px;
    font-weight: 600;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 0 20px 16px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 16px 12px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon {
    font-size: 20px;
    margin-bottom: 8px;
    display: block;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 2px;
}

.stat-label {
    font-size: 10px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Settings Section */
.settings-section {
    padding: 0 20px 16px;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-right: 12px;
}

.section-divider {
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, #e5e7eb 0%, transparent 100%);
}

.settings-grid {
    display: grid;
    gap: 12px;
}

.setting-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.setting-card:hover {
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

.setting-card.full-width {
    grid-column: 1 / -1;
}

.setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.setting-title {
    font-weight: 600;
    font-size: 14px;
    color: #1a1a1a;
}

.setting-description {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

/* Premium Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e5e7eb;
    transition: 0.3s ease;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* Premium Select */
.premium-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    background: white;
    color: #1a1a1a;
    cursor: pointer;
    transition: all 0.2s ease;
}

.premium-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Actions Section */
.actions-section {
    padding: 0 20px 16px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 8px;
    border: none;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-height: 60px;
}

.btn-icon {
    font-size: 18px;
    margin-bottom: 4px;
    display: block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 65, 108, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 65, 108, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #1a1a1a;
    box-shadow: 0 4px 12px rgba(168, 237, 234, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(168, 237, 234, 0.4);
}

/* Footer */
.footer {
    margin-top: auto;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.8);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 0 0 16px 16px;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 11px;
    color: #6b7280;
}

.version {
    font-weight: 600;
    color: #667eea;
}

.separator {
    opacity: 0.5;
}

.status-text {
    font-weight: 500;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.container {
    animation: fadeIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-height: 600px) {
    .container {
        margin: 4px;
    }

    .performance-section,
    .settings-section,
    .actions-section {
        padding-left: 16px;
        padding-right: 16px;
    }

    .header {
        padding: 16px;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility */
.btn:focus,
.toggle-switch input:focus + .toggle-slider,
.premium-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Will be implemented if needed */
}
