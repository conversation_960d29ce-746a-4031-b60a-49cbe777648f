<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas" width="128" height="128" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon(16)">Download 16x16</button>
    <button onclick="downloadIcon(32)">Download 32x32</button>
    <button onclick="downloadIcon(48)">Download 48x48</button>
    <button onclick="downloadIcon(128)">Download 128x128</button>
    
    <script>
        function drawIcon(size) {
            const canvas = document.getElementById('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.47;
            
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fillStyle = '#ff0000';
            ctx.fill();
            ctx.strokeStyle = '#cc0000';
            ctx.lineWidth = size * 0.016;
            ctx.stroke();
            
            // Play button triangle
            const triangleSize = size * 0.3;
            const triangleX = centerX - triangleSize * 0.2;
            const triangleY = centerY;
            
            ctx.beginPath();
            ctx.moveTo(triangleX, triangleY - triangleSize * 0.6);
            ctx.lineTo(triangleX, triangleY + triangleSize * 0.6);
            ctx.lineTo(triangleX + triangleSize * 0.8, triangleY);
            ctx.closePath();
            ctx.fillStyle = 'white';
            ctx.fill();
            
            // Progress bar
            const barWidth = size * 0.69;
            const barHeight = size * 0.063;
            const barX = (size - barWidth) / 2;
            const barY = size * 0.78;
            
            // Background bar
            ctx.beginPath();
            ctx.roundRect(barX, barY, barWidth, barHeight, barHeight / 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fill();
            
            // Progress fill
            ctx.beginPath();
            ctx.roundRect(barX, barY, barWidth * 0.5, barHeight, barHeight / 2);
            ctx.fillStyle = '#ffeb3b';
            ctx.fill();
            
            // Clock icon
            const clockSize = size * 0.094;
            const clockX = size * 0.78;
            const clockY = size * 0.22;
            
            ctx.beginPath();
            ctx.arc(clockX, clockY, clockSize, 0, 2 * Math.PI);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = size * 0.016;
            ctx.stroke();
            
            // Clock hands
            ctx.beginPath();
            ctx.moveTo(clockX, clockY - clockSize * 0.5);
            ctx.lineTo(clockX, clockY);
            ctx.lineTo(clockX + clockSize * 0.3, clockY + clockSize * 0.3);
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = size * 0.016;
            ctx.stroke();
        }
        
        function downloadIcon(size) {
            drawIcon(size);
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Draw initial icon
        drawIcon(128);
    </script>
</body>
</html>
