<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Progress Saver</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header with gradient background -->
        <div class="header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M8 5v14l11-7z" fill="currentColor"/>
                            <rect x="2" y="18" width="20" height="2" rx="1" fill="currentColor" opacity="0.6"/>
                            <rect x="2" y="18" width="10" height="2" rx="1" fill="#FFD700"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1>YouTube Progress</h1>
                        <span class="subtitle">Smart Resume</span>
                    </div>
                </div>
                <div class="status-badge" id="statusBadge">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <span id="statusText">Active</span>
                </div>
            </div>
        </div>

        <!-- Performance Monitor -->
        <div class="performance-section">
            <div class="performance-card">
                <div class="performance-header">
                    <span class="performance-title">Performance</span>
                    <div class="performance-indicator good" id="performanceIndicator"></div>
                </div>
                <div class="performance-stats">
                    <div class="performance-item">
                        <span class="performance-label">Memory</span>
                        <span class="performance-value" id="memoryUsage">-</span>
                    </div>
                    <div class="performance-item">
                        <span class="performance-label">CPU</span>
                        <span class="performance-value" id="cpuUsage">Low</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-icon videos">📹</div>
                <div class="stat-content">
                    <span class="stat-number" id="videoCount">-</span>
                    <span class="stat-label">Videos Tracked</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon storage">💾</div>
                <div class="stat-content">
                    <span class="stat-number" id="storageUsed">-</span>
                    <span class="stat-label">Storage Used</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon time">⏱️</div>
                <div class="stat-content">
                    <span class="stat-number" id="saveInterval">5s</span>
                    <span class="stat-label">Save Interval</span>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div class="settings-section">
            <div class="section-header">
                <h3>Settings</h3>
                <div class="section-divider"></div>
            </div>

            <div class="settings-grid">
                <div class="setting-card">
                    <div class="setting-header">
                        <span class="setting-title">Progress Saving</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enabledToggle" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <p class="setting-description">Automatically save video progress</p>
                </div>

                <div class="setting-card">
                    <div class="setting-header">
                        <span class="setting-title">Crash Recovery</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="crashRecoveryToggle" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <p class="setting-description">Resume videos after browser crash</p>
                </div>

                <div class="setting-card">
                    <div class="setting-header">
                        <span class="setting-title">Single Video Mode</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="singleVideoToggle" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <p class="setting-description">Only track current video</p>
                </div>

                <div class="setting-card full-width">
                    <div class="setting-header">
                        <span class="setting-title">Save Interval</span>
                        <select id="saveIntervalSelect" class="premium-select">
                            <option value="3">3 seconds</option>
                            <option value="5" selected>5 seconds</option>
                            <option value="10">10 seconds</option>
                            <option value="15">15 seconds</option>
                        </select>
                    </div>
                    <p class="setting-description">How often to save progress</p>
                </div>
            </div>
        </div>

        <!-- Actions Section -->
        <div class="actions-section">
            <div class="section-header">
                <h3>Actions</h3>
                <div class="section-divider"></div>
            </div>

            <div class="action-buttons">
                <button id="clearCurrentData" class="btn btn-primary">
                    <span class="btn-icon">🎯</span>
                    Clear Current
                </button>
                <button id="clearData" class="btn btn-danger">
                    <span class="btn-icon">🗑️</span>
                    Clear All
                </button>
                <button id="exportData" class="btn btn-secondary">
                    <span class="btn-icon">📤</span>
                    Export
                </button>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <span class="version">v1.0.0</span>
                <span class="separator">•</span>
                <span class="status-text" id="footerStatus">Ready</span>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
