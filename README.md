# YouTube Watch Progress Saver

A Chrome browser extension that automatically saves your YouTube video progress and resumes playback from where you left off.

## Features

- **Automatic Progress Saving**: Saves your video progress every 5 seconds (configurable)
- **Auto Resume**: Automatically resumes videos from where you left off
- **No UI Interference**: Works silently in the background
- **Local Storage**: All data is stored locally in your browser
- **Privacy Focused**: No data is sent to external servers
- **Configurable Settings**: Adjust save intervals and other preferences
- **Data Management**: View statistics, export data, or clear all saved progress

## Installation

### From Source (Development)

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension folder
5. The extension should now appear in your extensions list

### Icon Setup (Optional)

The extension includes an SVG icon template. For production use, convert the SVG to PNG format:

```bash
# Convert SVG to different PNG sizes (requires imagemagick or similar)
convert icons/icon.svg -resize 16x16 icons/icon16.png
convert icons/icon.svg -resize 32x32 icons/icon32.png
convert icons/icon.svg -resize 48x48 icons/icon48.png
convert icons/icon.svg -resize 128x128 icons/icon128.png
```

Or use online SVG to PNG converters to create the required icon sizes.

## How It Works

1. **Detection**: The extension detects when you're watching a YouTube video
2. **Tracking**: It monitors the video player and saves your progress every few seconds
3. **Storage**: Progress is saved locally using Chrome's storage API
4. **Resume**: When you return to a video, it automatically seeks to your last position

## Technical Details

### Files Structure

- `manifest.json` - Extension configuration and permissions
- `content.js` - Main script that runs on YouTube pages
- `background.js` - Service worker for background tasks
- `popup.html` - Extension popup interface
- `popup.js` - Popup functionality and settings
- `styles.css` - Popup styling
- `icons/` - Extension icons

### Key Features

- **Smart Saving**: Only saves progress after 10 seconds of watching and stops saving near the end
- **SPA Navigation**: Handles YouTube's single-page application navigation
- **Error Handling**: Robust error handling for various edge cases
- **Performance**: Minimal impact on YouTube's performance
- **Cleanup**: Automatically removes old progress data (30+ days)

### Storage Format

Progress data is stored with the following structure:

```json
{
  "yt_progress_[VIDEO_ID]": {
    "videoId": "VIDEO_ID",
    "timestamp": 123.45,
    "duration": 600.0,
    "lastSaved": 1693123456789,
    "url": "https://youtube.com/watch?v=VIDEO_ID"
  }
}
```

## Configuration

Access the extension popup to configure:

- **Enable/Disable**: Toggle progress saving on/off
- **Save Interval**: Choose how often to save progress (3-15 seconds)
- **Statistics**: View number of tracked videos and storage usage
- **Data Management**: Clear all data or export for backup

## Privacy

- All data is stored locally in your browser
- No data is transmitted to external servers
- No tracking or analytics
- No access to personal information

## Troubleshooting

### Extension Not Working

1. Check that the extension is enabled in `chrome://extensions/`
2. Refresh the YouTube page
3. Check the browser console for any error messages

### Progress Not Saving

1. Ensure the extension has permission to access YouTube
2. Check that you've watched the video for at least 10 seconds
3. Verify storage permissions in the extension settings

### Videos Not Resuming

1. Make sure you're watching the same video (same video ID)
2. Check that progress was saved (visible in popup statistics)
3. Try refreshing the page

## Development

### Testing

1. Load the extension in developer mode
2. Navigate to any YouTube video
3. Watch for console messages indicating progress saving
4. Close and reopen the video to test resume functionality

### Debugging

- Check the browser console on YouTube pages for content script logs
- Use the extension popup to view statistics and settings
- Inspect `chrome://extensions/` for any extension errors

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the extension.

## License

This project is open source and available under the MIT License.

## Version History

- **v1.0.0** - Initial release with core functionality
  - Automatic progress saving
  - Auto resume functionality
  - Configurable settings
  - Data management features
