// YouTube Watch Progress Saver - Background Script (Service Worker)

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
    console.log('YouTube Progress Saver installed');

    if (details.reason === 'install') {
        // Set default settings on first install
        chrome.storage.local.set({
            'yt_progress_enabled': true,
            'yt_progress_save_interval': 5000,
            'yt_progress_min_watch_time': 10,
            'yt_progress_crash_recovery': true,
            'yt_progress_single_video_mode': true
        });
    }

    // Run cleanup on install/update
    cleanupOldProgress();
});

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'saveProgress') {
        // Handle progress saving if needed
        console.log('Progress save request:', request.data);
        sendResponse({success: true});
    }

    if (request.action === 'getSettings') {
        // Return current settings
        chrome.storage.local.get([
            'yt_progress_enabled',
            'yt_progress_save_interval',
            'yt_progress_min_watch_time',
            'yt_progress_crash_recovery',
            'yt_progress_single_video_mode'
        ]).then(settings => {
            sendResponse(settings);
        });
        return true; // Will respond asynchronously
    }

    if (request.action === 'saveCrashRecovery') {
        // Save current video for crash recovery
        console.log('🔄 Background: Saving crash recovery data:', request.data);
        chrome.storage.local.set({
            'yt_progress_last_video': request.data
        });
        sendResponse({success: true});
    }

    if (request.action === 'getCrashRecovery') {
        // Get last video for crash recovery
        console.log('🔄 Background: Getting crash recovery data');
        chrome.storage.local.get('yt_progress_last_video').then(result => {
            console.log('🔄 Background: Crash recovery data retrieved:', result.yt_progress_last_video);
            sendResponse(result.yt_progress_last_video || null);
        });
        return true;
    }

    if (request.action === 'clearCrashRecovery') {
        // Clear crash recovery data
        console.log('🔄 Background: Clearing crash recovery data');
        chrome.storage.local.remove('yt_progress_last_video').then(() => {
            sendResponse({success: true});
        });
        return true;
    }
});

// Clean up old progress data
function cleanupOldProgress() {
    chrome.storage.local.get(null).then(items => {
        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000); // 30 days

        const keysToRemove = [];

        for (const [key, value] of Object.entries(items)) {
            if (key.startsWith('yt_progress_') &&
                key !== 'yt_progress_enabled' &&
                key !== 'yt_progress_save_interval' &&
                key !== 'yt_progress_min_watch_time' &&
                key !== 'yt_progress_crash_recovery' &&
                key !== 'yt_progress_last_video' &&
                value.lastSaved &&
                value.lastSaved < thirtyDaysAgo) {
                keysToRemove.push(key);
            }
        }

        if (keysToRemove.length > 0) {
            chrome.storage.local.remove(keysToRemove);
            console.log(`Cleaned up ${keysToRemove.length} old progress entries`);
        }
    }).catch(error => {
        console.error('Cleanup failed:', error);
    });
}
