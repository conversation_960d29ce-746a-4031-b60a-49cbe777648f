// YouTube Watch Progress Saver - Background Script (Service Worker)

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
    console.log('YouTube Progress Saver installed');
    
    if (details.reason === 'install') {
        // Set default settings on first install
        chrome.storage.local.set({
            'yt_progress_enabled': true,
            'yt_progress_save_interval': 5000,
            'yt_progress_min_watch_time': 10
        });
    }
});

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'saveProgress') {
        // Handle progress saving if needed
        console.log('Progress save request:', request.data);
        sendResponse({success: true});
    }
    
    if (request.action === 'getSettings') {
        // Return current settings
        chrome.storage.local.get([
            'yt_progress_enabled',
            'yt_progress_save_interval',
            'yt_progress_min_watch_time'
        ]).then(settings => {
            sendResponse(settings);
        });
        return true; // Will respond asynchronously
    }
});

// Clean up old progress data (optional - runs on startup)
function cleanupOldProgress() {
    chrome.storage.local.get(null).then(items => {
        const now = Date.now();
        const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000); // 30 days

        const keysToRemove = [];

        for (const [key, value] of Object.entries(items)) {
            if (key.startsWith('yt_progress_') && value.lastSaved && value.lastSaved < thirtyDaysAgo) {
                keysToRemove.push(key);
            }
        }

        if (keysToRemove.length > 0) {
            chrome.storage.local.remove(keysToRemove);
            console.log(`Cleaned up ${keysToRemove.length} old progress entries`);
        }
    });
}

// Run cleanup on extension startup
chrome.runtime.onStartup.addListener(() => {
    cleanupOldProgress();
});
